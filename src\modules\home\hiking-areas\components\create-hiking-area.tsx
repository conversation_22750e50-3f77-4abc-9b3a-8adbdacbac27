"use client";

import React, { useState } from "react";
import { useRouter } from "next/navigation";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Textarea } from "@/components/ui/textarea";
import { IHikingArea, IHikingAreaItem } from "@/types/home";
import { toast } from "sonner";
import { useGetHome } from "../../queries/get-home";
import { UseCreateHikingArea } from "../mutations/use-create-hiking-area";

const CreateHikingAreaPage: React.FC = () => {
    const router = useRouter();
    const createHikingArea = UseCreateHikingArea();

    // Fetch home data
    const {
        data: homeData,
        isLoading: homeLoading,
        error: homeError,
    } = useGetHome();

    // State for main hiking area block
    const [heading, setHeading] = useState("");
    const [subHeading, setSubHeading] = useState("");
    const [areas, setAreas] = useState<IHikingAreaItem[]>([]);

    // Temporary state for a new area input
    const [newArea, setNewArea] = useState<IHikingAreaItem>({
        id: "",
        homeHikingId: "",
        title: "",
        subtitle: "",
        image: "",
        linkUrl: "",
        createdAt: "",
        updatedAt: "",
    });

    if (homeLoading) return <p>Loading home data...</p>;
    if (homeError) return <p>Error loading home data: {homeError.message} </p>;

    const homeId = homeData?.data.id ?? "";

    const handleAddArea = () => {
        if (!newArea.title.trim()) {
            toast.error("Area title is required");
            return;
        }
        setAreas([
            ...areas,
            {
                ...newArea,
                id: crypto.randomUUID(),
                createdAt: new Date().toISOString(),
                updatedAt: new Date().toISOString(),
                homeHikingId: homeId,
            },
        ]);
        // Reset newArea
        setNewArea({
            id: "",
            homeHikingId: "",
            title: "",
            subtitle: "",
            image: "",
            linkUrl: "",
            createdAt: "",
            updatedAt: "",
        });
    };

    const handleSubmit = (e: React.FormEvent) => {
        e.preventDefault();

        if (!homeId) {
            toast.error("Home ID is required");
            return;
        }

        if (areas.length === 0) {
            toast.error("Please add at least one area");
            return;
        }

        const payload: IHikingArea = {
            id: crypto.randomUUID(),
            heading,
            subHeading,
            homeId,
            createdAt: new Date().toISOString(),
            updatedAt: new Date().toISOString(),
            areas,
        };

        createHikingArea.mutate(payload, {
            onSuccess: () => {
                toast.success("Hiking Area created successfully");
                router.push("/hiking-areas");
            },
            onError: () => {
                toast.error("Failed to create hiking area");
            },
        });
    };

    return (
        <div className="container mx-auto p-6 max-w-3xl">
            <h1 className="text-3xl font-bold mb-6" > Create Hiking Area </h1>

            < form onSubmit={handleSubmit} className="space-y-6" >
                {/* Heading */}
                < div >
                    <label className="block text-sm font-medium mb-1" > Heading </label>
                    < Input
                        value={heading}
                        onChange={(e) => setHeading(e.target.value)}
                        placeholder="Enter main heading"
                        required
                    />
                </div>

                {/* Subheading */}
                <div>
                    <label className="block text-sm font-medium mb-1" > Sub Heading </label>
                    < Textarea
                        value={subHeading}
                        onChange={(e) => setSubHeading(e.target.value)}
                        placeholder="Enter sub heading"
                    />
                </div>

                {/* Add Areas */}
                <div>
                    <h2 className="text-xl font-semibold mb-2" > Add Areas </h2>
                    < div className="grid gap-4 border p-4 rounded mb-4" >
                        <Input
                            placeholder="Title"
                            value={newArea.title}
                            onChange={(e) =>
                                setNewArea({ ...newArea, title: e.target.value })
                            }
                        />
                        < Input
                            placeholder="Subtitle"
                            value={newArea.subtitle}
                            onChange={(e) =>
                                setNewArea({ ...newArea, subtitle: e.target.value })
                            }
                        />
                        < Input
                            placeholder="Image URL"
                            value={newArea.image}
                            onChange={(e) =>
                                setNewArea({ ...newArea, image: e.target.value })
                            }
                        />
                        < Input
                            placeholder="Link URL"
                            value={newArea.linkUrl}
                            onChange={(e) =>
                                setNewArea({ ...newArea, linkUrl: e.target.value })
                            }
                        />
                        < Button type="button" onClick={handleAddArea} >
                            + Add Area
                        </Button>
                    </div>

                    {/* Preview Areas Table */}
                    {
                        areas.length > 0 && (
                            <table className="min-w-full border border-gray-300" >
                                <thead>
                                    <tr className="bg-gray-50" >
                                        <th className="border px-2 py-1" > Title </th>
                                        < th className="border px-2 py-1" > Subtitle </th>
                                        < th className="border px-2 py-1" > Link </th>
                                    </tr>
                                </thead>
                                <tbody>
                                    {
                                        areas.map((area) => (
                                            <tr key={area.id} className="even:bg-gray-50" >
                                                <td className="border px-2 py-1" > {area.title} </td>
                                                < td className="border px-2 py-1" > {area.subtitle} </td>
                                                < td className="border px-2 py-1" > {area.linkUrl} </td>
                                            </tr>
                                        ))
                                    }
                                </tbody>
                            </table>
                        )
                    }
                </div>

                {/* Buttons */}
                <div className="flex justify-end space-x-3" >
                    <Button
                        type="button"
                        variant="outline"
                        onClick={() => router.push("/hiking-areas")}
                    >
                        Cancel
                    </Button>
                    < Button type="submit" >
                        {createHikingArea.isLoading ? "Saving..." : "Create Hiking Area"}
                    </Button>
                </div>
            </form>
        </div>
    );
};

export default CreateHikingAreaPage;
